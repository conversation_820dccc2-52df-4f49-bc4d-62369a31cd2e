{"version": 3, "file": "bindCallbackInternals.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/bindCallbackInternals.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,MAAM,UAAU,qBAAqB,CACnC,WAAoB,EACpB,YAAiB,EACjB,cAAoB,EACpB,SAAyB;IAEzB,IAAI,cAAc,EAAE;QAClB,IAAI,WAAW,CAAC,cAAc,CAAC,EAAE;YAC/B,SAAS,GAAG,cAAc,CAAC;SAC5B;aAAM;YAEL,OAAO;gBAAqB,cAAc;qBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;oBAAd,yBAAc;;gBACxC,OAAQ,qBAAqB,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,CAAS;qBACxE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;qBACjB,IAAI,CAAC,gBAAgB,CAAC,cAAqB,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC;SACH;KACF;IAID,IAAI,SAAS,EAAE;QACb,OAAO;YAAqB,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YACxC,OAAQ,qBAAqB,CAAC,WAAW,EAAE,YAAY,CAAS;iBAC7D,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;iBACjB,IAAI,CAAC,WAAW,CAAC,SAAU,CAAC,EAAE,SAAS,CAAC,SAAU,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC;KACH;IAED,OAAO;QAAA,iBAgFN;QAhF2B,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QAGxC,IAAM,OAAO,GAAG,IAAI,YAAY,EAAO,CAAC;QAGxC,IAAI,aAAa,GAAG,IAAI,CAAC;QACzB,OAAO,IAAI,UAAU,CAAC,UAAC,UAAU;YAE/B,IAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE3C,IAAI,aAAa,EAAE;gBACjB,aAAa,GAAG,KAAK,CAAC;gBAMtB,IAAI,SAAO,GAAG,KAAK,CAAC;gBAGpB,IAAI,YAAU,GAAG,KAAK,CAAC;gBAKvB,YAAY,CAAC,KAAK,CAEhB,KAAI,yCAGC,IAAI;oBAEP;wBAAC,iBAAiB;6BAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;4BAAjB,4BAAiB;;wBAChB,IAAI,WAAW,EAAE;4BAIf,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;4BAC5B,IAAI,GAAG,IAAI,IAAI,EAAE;gCACf,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAGnB,OAAO;6BACR;yBACF;wBAKD,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBAGxD,YAAU,GAAG,IAAI,CAAC;wBAMlB,IAAI,SAAO,EAAE;4BACX,OAAO,CAAC,QAAQ,EAAE,CAAC;yBACpB;oBACH,CAAC;mBAEJ,CAAC;gBAIF,IAAI,YAAU,EAAE;oBACd,OAAO,CAAC,QAAQ,EAAE,CAAC;iBACpB;gBAID,SAAO,GAAG,IAAI,CAAC;aAChB;YAGD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}