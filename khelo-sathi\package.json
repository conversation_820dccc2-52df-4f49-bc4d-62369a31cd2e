{"name": "khelo-sathi", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "socket": "ts-node server/socket.ts", "dev:all": "concurrently \"npm run dev\" \"npm run socket\""}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@hookform/resolvers": "^5.2.1", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.1", "@types/socket.io-client": "^1.4.36", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "compression": "^1.8.1", "concurrently": "^9.2.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "express": "^5.1.0", "framer-motion": "^12.23.12", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.535.0", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "next": "15.4.5", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "ts-node": "^10.9.2", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}