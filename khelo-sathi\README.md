# 🏆 KheloSathi - Sports Community Platform

**Find Players, Make Friends, Play Anywhere**

KheloSathi is a comprehensive sports matchmaking and community platform that connects sports enthusiasts, enables event creation, and builds lasting sports relationships. Built with modern web technologies and real-time features.

## ✨ Features

### 🧑‍🤝‍🧑 **User Management & Matchmaking**
- **Smart User Discovery**: Find nearby players using geolocation and advanced filters
- **Skill-Based Matching**: Match with players of similar skill levels
- **Friend System**: Send/receive friend requests and build your sports network
- **Profile Verification**: Aadhar, phone, and email verification for trust and safety

### 🗓️ **Event Management**
- **Create Events**: Organize matches, tournaments, training sessions
- **Join Events**: Discover and participate in nearby sports events
- **Capacity Management**: Set min/max participants with waitlist functionality
- **Event Types**: Support for matches, tournaments, training, and social events

### 💬 **Real-time Communication**
- **Live Chat**: Real-time messaging with Socket.IO
- **Group Chats**: Event-specific and private group conversations
- **Typing Indicators**: See when others are typing
- **Message Status**: Read receipts and delivery confirmations

### 📱 **Social Features**
- **Activity Feed**: Instagram-style posts with images and achievements
- **Leaderboards**: Track progress and compete with friends
- **Badges & Achievements**: Gamification elements for engagement
- **Location Sharing**: Share your location during events

### 🗺️ **Location & Maps**
- **Google Maps Integration**: Find nearby venues, events, and players
- **Geospatial Queries**: Efficient location-based search with MongoDB
- **Venue Discovery**: Discover sports facilities and courts nearby

## 🛠️ Tech Stack

### **Frontend**
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Smooth animations and transitions
- **Lucide React** - Beautiful icon library
- **React Hook Form** - Form handling with validation

### **Backend**
- **Node.js + Express.js** - RESTful API server
- **MongoDB + Mongoose** - Database with ODM
- **Socket.IO** - Real-time bidirectional communication
- **JWT Authentication** - Secure user authentication
- **Cloudinary** - Image upload and management

### **Additional Tools**
- **Google Maps API** - Location services
- **Google OAuth** - Social authentication
- **Nodemailer** - Email notifications
- **bcryptjs** - Password hashing
- **Zod** - Schema validation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB (local or cloud)
- Google Maps API key
- Cloudinary account (for image uploads)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd khelo-sathi
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
Create a `.env.local` file in the root directory:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/khelo-sathi
MONGODB_DB=khelo-sathi

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Cloudinary for image uploads
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Email (for notifications)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password

# Socket.IO
SOCKET_IO_PORT=3001

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
```

4. **Start the development servers**

Option 1: Start both frontend and Socket.IO server together
```bash
npm run dev:all
```

Option 2: Start them separately
```bash
# Terminal 1 - Frontend
npm run dev

# Terminal 2 - Socket.IO Server
npm run socket
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
khelo-sathi/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── users/         # User management APIs
│   │   │   └── events/        # Event management APIs
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Landing page
│   ├── lib/                   # Utility libraries
│   │   ├── mongodb.ts         # Database connection
│   │   ├── auth.ts            # Authentication helpers
│   │   └── utils.ts           # Common utilities
│   ├── models/                # MongoDB schemas
│   │   ├── User.ts            # User model
│   │   ├── Event.ts           # Event model
│   │   ├── Post.ts            # Post model
│   │   └── Chat.ts            # Chat model
│   └── types/                 # TypeScript type definitions
│       └── index.ts           # Shared types
├── server/
│   └── socket.ts              # Socket.IO server
├── public/                    # Static assets
├── .env.local                 # Environment variables
├── tailwind.config.ts         # Tailwind configuration
└── package.json               # Dependencies and scripts
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login

### Users
- `GET /api/users/profile` - Get current user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/discover` - Discover nearby users
- `POST /api/users/friends/request` - Send friend request
- `GET /api/users/friends/request` - Get friend requests
- `POST /api/users/friends/accept` - Accept friend request
- `POST /api/users/friends/reject` - Reject friend request

### Events
- `GET /api/events` - Get events with filters
- `POST /api/events` - Create new event
- `GET /api/events/:id` - Get event details
- `PUT /api/events/:id` - Update event
- `POST /api/events/:id/join` - Join event
- `POST /api/events/:id/leave` - Leave event

## 🎨 UI/UX Features

### **Dark Theme Design**
- Modern glassmorphism effects
- Gradient backgrounds and text
- Smooth animations with Framer Motion
- Responsive design for all devices

### **Interactive Elements**
- Hover effects and micro-interactions
- Loading states and skeleton screens
- Toast notifications for user feedback
- Modal dialogs for forms and confirmations

## 🔐 Security Features

- **JWT Authentication** with secure token handling
- **Password Hashing** with bcryptjs
- **Input Validation** with Zod schemas
- **Rate Limiting** on API endpoints
- **CORS Configuration** for secure cross-origin requests
- **User Verification** system for trust and safety

## 📱 Mobile Responsiveness

The application is fully responsive and optimized for:
- **Desktop** (1024px+)
- **Tablet** (768px - 1023px)
- **Mobile** (320px - 767px)

## 🚀 Deployment

### **Frontend (Vercel)**
```bash
npm run build
# Deploy to Vercel
```

### **Backend (Railway/Render)**
- Deploy the Socket.IO server separately
- Configure environment variables
- Set up MongoDB Atlas for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js** team for the amazing framework
- **Tailwind CSS** for the utility-first approach
- **Socket.IO** for real-time capabilities
- **MongoDB** for flexible data storage
- **Framer Motion** for smooth animations

---

**Built with ❤️ for the sports community**

*Find your next game partner today!* 🏆
